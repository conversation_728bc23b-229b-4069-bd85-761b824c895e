const {WeaDateGroup, Wea<PERSON><PERSON>er, WeaLocaleProvider} = ecCom
const {Button} = antd;
const {getLabel} = WeaLocaleProvider;

const singleCustomType = "browser.proj";

const img2Props = {
    src: "/cloudstore/release/${appId}/resources/title.png",
    height: 32,
};
//读取config
const config = ecodeSDK.getCom("${appId}", "config");


class Main extends React.Component {
    constructor(props) {
        super(props);
        let currentYear = new Date().getFullYear() + "";
        this.state = {
            searchSort: "1",//默认按照时间正序查询数据，0正序，1倒序
            buttonAscLoading: false,
            buttonLoading: false,
            currentYear: currentYear,
            xmid: "",
            xmData: [],
            reporter: "",
            startDate: "",
            endDate: "",
            dataGroupValue: ["-1"], //默认全部
            xm_viewAttr: 2,//项目查询条件，显示属性
            fixed_manager: "",//固定客户经理id
            fixed_manager_name: "",//固定客户经理名称
            fixed_hyczname: "",//固定行业产轴
            fixed_display: "none",
            normal_display: "flex",
        }
    }

    componentWillMount() {
    }

    componentDidMount() {
        let that = this;
        //判断是否有url参数
        const {util} = window.GRSDK
        let xm = util.getBrowserUrlParam("xm");
        let hidetitle = util.getBrowserUrlParam("hidetitle");

        let xmmc = "";
        let xmData = [];
        let xm_viewAttr = 2;
        let display_title = "flex";
        if (hidetitle && hidetitle + "" === "1") {
            display_title = "none";
        }
        if (xm) {
            xmmc = this.getProjectName(xm);
            xm_viewAttr = 1;
            xmData = [{
                id: xm,
                name: xmmc,
            }]
        }
        this.setState({
            xm_viewAttr: xm_viewAttr,
            xm: xm,
            display_title: display_title,
            xmData: xmData
        }, () => {
            that.refreshAll();
        })
    }

    getProjectName = (id) => {
        const {db} = window.GRSDK;
        let sql = "select ajmc from " + config.table_project + " where id =" + id;
        let result = db.query(sql);
        if (result && result.data && result.data.length > 0) {
            return result.data[0].ajmc;
        }
        return "";
    }

    /**
     * 设置子页面的ref，将子页面的this传递过来，方便当前页面调用
     * @param name
     * @returns {(function(*): void)|*}
     */
    setChildRef = (name) => (ref) => {
        this[name] = ref;
    }


    refreshAll = () => {
        const {xm, reporter, startDate, endDate, searchSort} = this.state;
        console.log("refreshAll", startDate, endDate);
        if (this.content) {
            this.content.refresh(xm, reporter, startDate, endDate, searchSort);
        }
    }

    /**
     * 正序查看搜索
     */
    clickRefreshAsc = () => {
        let that = this;
        this.setState({
            buttonAscLoading: true,
            searchSort: "0"
        }, () => {
            that.refreshAll();
            this.setState({buttonAscLoading: false});
        });
    }

    /**
     * 倒序搜索
     */
    clickRefresh = () => {
        let that = this;
        this.setState({
            buttonLoading: true,
            searchSort: "1"
        }, () => {
            that.refreshAll();
            this.setState({buttonLoading: false});
        });
    }

    /**
     * 选择提交人
     * @param ids
     * @param names
     * @param datas
     */
    selectPerson = (ids, names, datas) => {
        let that = this;
        console.log("选择提交人：", datas);
        this.setState({
            reporter: ids,
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择项目
     * @param ids
     * @param names
     * @param datas
     */
    selectProject = (ids, names, datas) => {
        let that = this;
        console.log("选择项目：", datas);
        this.setState({
            xmid: ids,
            xmData: datas
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择日期范围
     * @param value
     */
    selectDateGroup = (value) => {
        let that = this;
        console.log("选择日期范围", value);
        let dataRange = util.getDateRangeByValue(value);
        console.log("选择日期范围解析", dataRange);

        this.setState({
            dataGroupValue: value,
            startDate: dataRange.startDate,
            endDate: dataRange.endDate,
        }, () => {
            //如果没有解析出来范围，则不刷新
            //选择范围的时候
            if (value[0] + "" === "6") {
                if (dataRange.startDate && dataRange.endDate) {
                    that.refreshAll();
                }
            } else {
                that.refreshAll();
            }

        })
    }

    setFixedBar = (params) => {
        this.setState({
            normal_display: "none",
            fixed_display: "flex",
            ...params,
        })
    }
    renderSearchArea = () => {
        console.log("renderSearchArea11")
        return (
            <div className={"search"}>
                <div className="condition">
                    <div style={{
                        display: this.state.normal_display,
                        alignItems: "center"
                    }}>
                             <span className={"searchName"}>
                                项目
                            </span>
                        <WeaBrowser
                            type={161}
                            title="选择项目"
                            completeParams={{type: 161, fielddbtype: singleCustomType}}
                            conditionDataParams={{type: singleCustomType}}
                            dataParams={{
                                type: singleCustomType,
                            }}
                            destDataParams={{type: singleCustomType}}
                            {...defaultBrowserParams}
                            inputStyle={{width: 120}}
                            style={{
                                background: "white",
                            }}
                            replaceDatas={this.state.xmData}
                            viewAttr={this.state.xm_viewAttr}
                            onChange={this.selectProject}

                        />
                    </div>

                    <span className={"searchName"}>报告人</span>
                    <WeaBrowser
                        type={1}
                        title="人力资源"
                        tabs={[
                            {
                                name: getLabel(24515, "最近"),
                                key: "1"
                            },
                            {
                                name: getLabel(18511, "同部门"),
                                key: "2"
                            },
                            {
                                name: getLabel(15089, "我的下属"),
                                key: "3"
                            },
                            {
                                name: getLabel(18770, "按组织结构"),
                                key: "4",
                                browserProps: {
                                    browserTreeCustomProps: {
                                        defaultExpandedLevel: 2
                                    }
                                }
                            },
                            {
                                name: getLabel(81554, "常用组"),
                                key: "5"
                            },
                            {
                                name: "所有人",
                                key: "6"
                            }
                        ]}
                        showDls
                        linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                        isSingle={true}
                        inputStyle={{width: 120}}
                        onChange={this.selectPerson}
                    />
                    <span className={"searchName"}>
                            报告日期
                        </span>
                    <WeaDateGroup
                        isMobx
                        isInline
                        value={this.state.dataGroupValue}
                        datas={this.state.dataGroupDatas}
                        onChange={this.selectDateGroup}
                        style={{
                            width: "120px",
                            background: "white",
                            marginLeft: "10px"
                        }}
                    />
                </div>

                <div className={"button_area"}>
                    <Button type="primary" icon="reload" loading={this.state.buttonAscLoading}
                            onClick={this.clickRefreshAsc}>
                        正序查看
                    </Button>
                    <Button type="primary" icon="reload" loading={this.state.buttonLoading}
                            onClick={this.clickRefresh}>
                        搜索
                    </Button>
                </div>
            </div>
        )
    }

    renderFixedArea = () => {
        return (
            <div className={"fixed_field_bar"} style={{
                display: this.state.fixed_display
            }}>
                <div className="fixed_field">
                    <span className="label">项目名称：</span>
                    <span className="value">{this.state.fixed_xmmc}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">项目编号：</span>
                    <span className="value">{this.state.fixed_xmbh}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">客户：</span>
                    <span className="value">{this.state.fixed_khmc}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">客户经理：</span>
                    <span className="value">{this.state.fixed_manager_name}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">行业：</span>
                    <span className="value">{this.state.fixed_hycz_names}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">产品品牌：</span>
                    <span className="value">{this.state.fixed_cppp_names}</span>
                </div>
                <div className="fixed_field">
                    <span className="label">产品经理：</span>
                    <span className="value">{this.state.fixed_cpjl_names}</span>
                </div>
            </div>
        )
    }

    /**
     * 渲染
     */
    render() {
        return (
            <div className={"SD_Page"}>
                <div className={"top-toolbar"}>
                    <div className={"title"} style={{
                        display: this.state.display_title
                    }}>
                        <img {...img2Props} alt={"案件报告"}/>
                        <span>案件报告</span>
                    </div>
                    {this.renderSearchArea()}
                </div>
                {this.renderFixedArea()}
                <Content {...this.props} setPageRef={this.setChildRef("content")} setFixedBar={this.setFixedBar}/>
            </div>

        )
    }
}