const {Wea<PERSON>rowser, WeaSlideModal} = ecCom
const {Spin} = antd

const slide_report = (
    <iframe id="iframe_report" width="100%" height="100%" style={{
        height: "100vh",
        paddingTop: "10px",
        paddingBottom: "10%"
    }}></iframe>
)

//读取config
const config = ecodeSDK.getCom("${appId}", "config");


class Content extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            option: {},
            hasData: true, // 默认有数据
            data: [], // 数据列表
            page: 1,
            pageSize: 10,
            loading: false, //接口读取数据的loading
            finished: false,
            canLoadMore: false, // 新增：只有下拉到提示后再继续下拉才加载
            visible_report: false,
            normal_display: "flex",
            dataKey: "",//建模查询的dataKey存储，第一次为空
            pageNo: 1,//初始查询第一页
        }
        this.scrollRef = React.createRef();
        this.listEndRef = React.createRef();
    }

    componentWillMount() {

    }

    componentDidMount() {
        this.props.setPageRef(this)
        const container = document.getElementById('container');
        if (container) {
            container.addEventListener('scroll', this.handleScroll);
        }
    }

    componentWillUnmount() {
        const container = document.getElementById('container');
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
    }

    /**
     * 刷新
     * @param xm
     * @param reporter
     * @param startdate
     * @param enddate
     * @param searchSort
     */
    refresh = (xm, reporter, startdate, enddate, searchSort) => {
        let that = this;
        this.setState({
            xm: xm,
            reporter: reporter,
            startdate: startdate,
            enddate: enddate,
            searchSort: searchSort,
            data: [], // 重置数据
            pageNo: 1, // 重置页码
            dataKey: "", // 重置dataKey
            finished: false, // 重置完成状态
            loadingmore: false // 重置加载更多状态
        }, () => {
            that.doRefresh(true)
        })
    }

    doRefresh = (firstRefresh) => {
        let that = this;
        const {xm, reporter, startdate, enddate, searchSort, dataKey, pageNo, data} = this.state;
        //使用接口获取数据
        const {http} = window.GRSDK
        let auth_customid = config.auth_desc_customid;//倒序
        if (searchSort + "" === "0") {
            auth_customid = config.auth_asc_customid;//正序
        }
        let params = {
            port: config.port,//当前服务器内网环境端口号（后端调用建模查询接口时使用）
            auth_customid: auth_customid,//权限建模查询id
            xm_paramname: config.xm_paramname,//权限查询条件字段id-客户
            bgr_paramname: config.bgr_paramname,//权限查询条件字段id-报告人
            bgrq_paramname: config.bgrq_paramname,//权限查询条件字段id-报告日期
            report_modid: config.report_modid,//报告建模id
            xm: xm, //查询条件-项目id
            bgr: reporter, //查询条件-报告人
            startdate: startdate, //查询条件-开始日期
            enddate: enddate, //查询条件-结束日期
            searchSort: searchSort,//查询时间顺序 0正序 1倒序
            table_report: config.table_report,
            table_project: config.table_project,
            dataKey: dataKey,
            pageNo: pageNo,
        }
        this.setState({
            loading: true
        })

        let finished = true;
        http.postAC(config.api_url, params, (result) => {
            console.log("获取报告数据result", result);
            let newData = data; // 默认使用当前数据
            if (result && result.status === true) {
                let apiData = result.data;
                if (apiData && apiData.length > 0) {
                    // 创建新数组而不是直接修改原数组，避免React重新渲染导致滚动位置重置
                    if (firstRefresh) {
                        newData = [...apiData]; // 首次刷新时替换所有数据
                    } else {
                        newData = [...data, ...apiData]; // 加载更多时追加数据
                    }
                    //如果当前apiData有数据，则代表还没有到底了
                    finished = false;
                } else if (firstRefresh) {
                    newData = []; // 首次刷新但无数据时清空
                }
            } else if (firstRefresh) {
                newData = []; // 首次刷新但请求失败时清空
            }
            let dataKey = result.dataKey || "";
            if (firstRefresh) {
                that.handleFirstRefresh(newData);
            } else {
                that.setState({
                    loadingmore: false
                });
            }
            that.setState({
                loading: false,
                finished: finished,
                dataKey: dataKey,
                data: newData,
            })
        });
    }

    /**
     * 处理第一次刷新
     * @param data
     */
    handleFirstRefresh = (data) => {
        const {util} = window.GRSDK
        let xm = util.getBrowserUrlParam("xm"); //具体项目id
        let list_gap_height = "28px";
        let normal_display = "flex";
        if (data.length > 0 && xm) {

            list_gap_height = "68px";
            normal_display = "none";
            let params = {
                fixed_xmid: data[0].xmid, //项目id
                fixed_xmmc: data[0].xmmc,//项目名称
                fixed_xmbh: data[0].xmbh,//项目编号
                fixed_khid: data[0].khid,//客户id
                fixed_khmc: data[0].khname, //客户名称
                fixed_manager: data[0].manager,
                fixed_manager_name: data[0].managername,//经理
                fixed_hycz_names: data[0].hyczname,//行业产轴
                fixed_cppp_names: data[0].cpppnames,
                fixed_cpjl_names: data[0].cpjlnames,
            }
            this.props.setFixedBar(params);

        }
        this.setState({
            normal_display: normal_display,
            list_gap_height: list_gap_height,
        })
    }

    handleScroll = () => {
        if (this.state.loading || this.state.finished || this.state.loadingmore) return;
        const container = document.getElementById('container');
        if (!container) return;
        const scrollTop = container.scrollTop;
        const clientHeight = container.clientHeight;
        const scrollHeight = container.scrollHeight;

        // 距离底部小于100px时自动加载
        if (scrollHeight - (scrollTop + clientHeight) < 200) {
            this.loadMore();
        }
    }

    loadMore = () => {
        let that = this;
        const {loading, finished, loadingmore, pageNo} = this.state;
        if (loading || finished || loadingmore) return;

        this.setState({
            pageNo: pageNo + 1,
            loadingmore: true,
        }, () => {
            that.doRefresh(false)
        })
    }


    renderCard = (item, idx) => {
        const {normal_display} = this.state;
        console.log("item", item)
        // PC端布局，左右分栏，右侧为评论
        return (
            <div className="card-pc card-pc-flex" key={item.id}>
                <div className="card-index" title={item.id}>{idx + 1}</div>
                <div className="card-pc-left">
                    {/* 左侧第一行 */}
                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">项目名称</span>
                        <span className="value"> {item.xmmc}</span>
                        <span className="label">项目编号</span>
                        <span className="value"> {item.xmbh}</span>
                    </div>

                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">客户名称</span>
                        <span className="value">
                                <a
                                    href={`/spa/crm/static/index.html#/main/crm/customerView?customerId=${item.khid}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {item.khname}
                                </a>
                            </span>
                        <span className="label">客户经理</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: item.manager, name: item.managername}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />

                            </span>
                    </div>

                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">行业</span>
                        <span className="value">{item.hycznames}</span>
                    </div>

                    <div className="row" style={{
                        display: normal_display
                    }}>
                        <span className="label">产品品牌</span>
                        <span className="value"> {item.cpppnames}</span>
                        <span className="label">产品经理</span>
                        <span className="value"> {item.cpjlnames}</span>
                    </div>


                    {/* 行 */}
                    <div className="row"><span className="label">报告日期</span>
                        <span className="value">
                                <a
                                    href="#"
                                    onClick={e => {
                                        e.preventDefault();
                                        this.openSlide(item.id);
                                    }}
                                >
                                    {item.rq}
                                </a>
                            </span>
                        <span className="label">报告人</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: item.tbr, name: item.tbrname}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />
                            </span>
                    </div>

                    {/* 行 */}
                    <div className="row align-field">
                        <span className="label">痛点描述</span>
                        <span className="value-area scrollable top-align high-plan">{item.ajtdms}</span>
                    </div>
                    {/* 行 */}
                    <div className="row align-field"><span className="label">跟进详情</span>
                        <span className="value-area scrollable top-align high-plan">{item.gjxq}</span>
                    </div>
                    <div className="row align-field"><span className="label">下期计划</span>
                        <span className="value-area scrollable top-align high-plan">{item.xqjh}</span>
                    </div>
                    {/* 行 */}
                    <div className="row">
                        <span className="label">附件</span>
                        <span className="value" style={{
                            wordBreak: "break-all",
                        }}>
                            {item.docList && item.docList.length > 0 ? item.docList.map((c, i) => (
                                <a style={{marginLeft: "5px"}}
                                   href="#"
                                   onClick={e => {
                                       e.preventDefault();
                                       this.clickAttach(c, item);
                                   }}
                                >
                                    {c.fileName}
                                </a>
                            )) : "(无)"}
                            </span>
                    </div>
                </div>
                <div className="card-pc-right">
                    <div className="row"><span className="label">评论</span></div>
                    <div className="row value-area scrollable top-align comment-full-height">
                        {item.commentList && item.commentList.length > 0 ? item.commentList.map((c, i) => (
                            <div className="comment-item" key={i}>
                                <div className="comment-meta">
                                    <WeaBrowser
                                        type={1}
                                        title="人力资源"
                                        showDls
                                        viewAttr={1}
                                        replaceDatas={[{id: c.replyor, name: c.replyorname}]}
                                        linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                        inputStyle={{width: 'auto'}}
                                        {...defaultBrowserParams}
                                    />
                                    <span className="comment-date">{c.replydate} {c.replytime}</span>
                                </div>
                                <div className="comment-content"
                                     dangerouslySetInnerHTML={{__html: util.processHtmlContent(c.replycontent)}}></div>
                            </div>
                        )) : "(无)"}

                    </div>
                </div>
            </div>
        )

    }

    /**
     * 打开附件
     * @param attach
     * @param item
     */
    clickAttach = (attach, item) => {
        console.log("clickAttach", attach, item);
        let moduleid = config.report_modid;
        let authorizeformmodeFieldId = config.authorizeformmodeFieldId;
        let currentUser = ecodeSDK.getEcodeParams(['ecode_params'])._user.id;
        let url = "/spa/document/index2file.jsp?f_weaver_belongto_userid=" + currentUser + "&f_weaver_belongto_usertype=0&id=" + attach.docid + "&formmode_authorize=formmode_authorize&moduleid=formmode&authorizemodeId=" + moduleid + "&authorizefieldid=" + authorizeformmodeFieldId + "&authorizeformmodebillId=" + item.id + "&imagefileId=" + attach.fileid + "&isFromAccessory=true&router=1#/main/document/fileView"
        window.open(url);
    }
    /**
     * 侧滑打开建模卡片
     * @param billid
     */
    openSlide = (billid) => {
        let moduleid = config.report_modid;
        let url = "/spa/cube/index.html#/main/cube/card?type=0&modeId=" + moduleid + "&billid=" + billid;
        $("#iframe_report").attr('src', url)
        this.setState({
            visible_report: true
        })
    }

    onCloseSlide = () => {
        this.setState({visible_report: false})
        console.log('close')
        $("#iframe_report").attr('src', '');

    }


    render() {
        const {
            finished, visible_report, loading, list_gap_height
        } = this.state;

        let content = (
            <div className={"content"}>
                <div className={"card-list-pc"} style={{
                    gap: list_gap_height
                }} ref={this.scrollRef}>
                    {visible_report && (
                        <div className={"mask"}
                             onClick={this.onCloseSlide}
                        />
                    )}
                    <WeaSlideModal visible={visible_report}
                                   top={10}
                                   width={80}
                                   height={100}
                                   direction={'right'}
                                   measure={'%'}
                                   title={''}
                                   content={slide_report}
                                   closeMaskOnClick={true}
                                   onClose={this.onCloseSlide}
                                   hasScroll={false}
                    />

                    {this.state.data.map(this.renderCard)}
                    {!finished && <div className="list-end" ref={this.listEndRef}>继续下拉显示更多↓</div>}
                    {finished && <div className="list-end">-----已经到底了-----</div>}
                </div>
            </div>
        )
        let loadingdiv = (
            <div className="card-loading">
                <Spin spinning={true} tip="正在读取数据..."/>
            </div>
        )
        return (
            <div>{loading ? loadingdiv : content}</div>

        )
    }
}
