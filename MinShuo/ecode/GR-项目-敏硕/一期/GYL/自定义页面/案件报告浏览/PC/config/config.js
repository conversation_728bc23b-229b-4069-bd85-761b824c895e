//设置config
const config = {
    port: "80",//当前服务器内网环境端口号（后端调用建模查询接口时使用）
    auth_asc_customid: "255",//权限建模查询id-报告日期正序
    auth_desc_customid: "251",//权限建模查询id-报告日期倒序
    xm_paramname: "12837",//权限查询条件字段id-项目
    bgr_paramname: "12835",//权限查询条件字段id-报告人
    bgrq_paramname: "12831",//权限查询条件字段id-报告日期
    report_modid: "169",//报告建模id
    api_url: "/api/sd/reportview/anjian/getData",//二开接口地址，获取销售报告数据
    authorizeformmodeFieldId: "12839", //建模-案件报告-相关附件字段id
    table_report: "formtable_main_328", //建模表名-项目跟进报告
    table_project: "formtable_main_329",////建模表名-项目
};
ecodeSDK.setCom("${appId}", "config", config);