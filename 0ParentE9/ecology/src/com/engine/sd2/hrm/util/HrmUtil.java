package com.engine.sd2.hrm.util;

import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.constant.DBTypeConst;
import com.engine.sd2.db.util.DBUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;

import java.util.*;

/**
 * @FileName HrmUtil.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/7/29
 */
public class HrmUtil {
    /**
     * 二开log类
     */
    private final static Logger log = LoggerFactory.getLogger(HrmUtil.class);

    public static Map<String, String> getHrmCodeMap() {
        Map<String, String> result = new HashMap<>();
        String sql = "select workcode,lastname from hrmresource where workcode is not null and workcode != '' ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(Util.null2String(rs.getString("workcode")), Util.null2String(rs.getString("lastname")));
            }
        }
        return result;
    }

    /**
     * 根据人员id集合，查询人员id对应的lastname的map
     *
     * @param allUserIdsSet
     * @return
     */
    public static Map<String, String> getHrmIdNameMap(Set<String> allUserIdsSet) {
        List<String> allIdsList = new ArrayList<>(allUserIdsSet);
        return getHrmIdNameMap(allIdsList);
    }

    /**
     * 根据人员id集合，查询人员id对应的lastname的map
     *
     * @param allUserIdsList
     * @return
     */
    public static Map<String, String> getHrmIdNameMap(List<String> allUserIdsList) {
        Map<String, String> map = new HashMap<>();
        //按1000个id分批次处理
        int batchSize = 1000;
        RecordSet rs = new RecordSet();
        for (int i = 0; i < allUserIdsList.size(); i += batchSize) {
            List<String> batchIds = allUserIdsList.subList(i, Math.min(i + batchSize, allUserIdsList.size()));
            String sql = "select id,lastname from hrmresource where id in (" + String.join(",", batchIds) + ")";
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    map.put(rs.getString("id"), rs.getString("lastname"));
                }
            }
        }
        return map;
    }


    /**
     * 获取人员id-对应-工号的map关系
     *
     * @return
     */
    public static Map<String, String> getHrmIdWorkCodeMap() {
        Map<String, String> result = new HashMap<>();
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        String sql = "select id,workcode from hrmresource";
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(rs.getString("id"), rs.getString("workcode"));
            }
        }
        return result;
    }

    public static Map<String, String> getSubCompanyCodeMap() {
        Map<String, String> result = new HashMap<>();
        String sql = "select subcompanycode,subcompanyname from hrmsubcompany where subcompanycode is not null and subcompanycode != '' ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(Util.null2String(rs.getString("subcompanycode")), Util.null2String(rs.getString("subcompanyname")));
            }
        }
        return result;
    }

    public static Map<String, String> getDeptCodeMap() {
        Map<String, String> result = new HashMap<>();
        String sql = "select departmentcode,departmentname from hrmdepartment where departmentcode is not null and departmentcode != '' ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(Util.null2String(rs.getString("departmentcode")), Util.null2String(rs.getString("departmentname")));
            }
        }
        return result;
    }

    /**
     * 校验某个人员id，是否属于某个角色，属于true，不属于false
     *
     * @param userId 人员id
     * @param roleId 角色id
     * @return
     */
    public static boolean checkUserInRole(int userId, int roleId) {
        HrmUserVarify hrmUserVarify = new HrmUserVarify();
        //获取角色等级
        String roleLevel = hrmUserVarify.getRightLevel(String.valueOf(userId), String.valueOf(roleId));
        // 如果有角色权限，返回true
        return hrmUserVarify.checkUserRight(String.valueOf(userId), String.valueOf(roleId), roleLevel);
    }

    /**
     * 查询一个人所有的上级人员id，包含查询人id本身
     *
     * @param userid
     * @return 人员id列表
     */
    public static Set<Integer> getAllManagerIdList(String userid) {
        Set<Integer> result = new HashSet<>();
        try {
            RecordSet rs = new RecordSet();
            String dbType = rs.getDBType(); //数据库类型
            //默认查询语法，支持mysql、postgresql、db2
            // 递归查询：找到上级的上级，直到没有上级为止
            String sql = "WITH RECURSIVE ManagerHierarchy AS ( " +
                    "    SELECT  " +
                    "        id,  " +
                    "        managerid " +
                    "    FROM  " +
                    "        hrmresource " +
                    "    WHERE  " +
                    "        id =   " + userid +
                    "    UNION ALL " +
                    "    SELECT  " +
                    "        h.id,  " +
                    "        h.managerid " +
                    "    FROM  " +
                    "        hrmresource h " +
                    "    INNER JOIN  " +
                    "        ManagerHierarchy mh ON h.id = mh.managerid " +
                    "     where h.id != mh.id " +// --  排除循环引用，但允许最高层的 managerid = id,这里是防止一个人的上级是自己，导致无限循环，这样会查到这种人为止
                    " ) " +
                    " SELECT  " +
                    "    id AS person_id, " +
                    "    managerid AS manager_id " +
                    " FROM  " +
                    "    ManagerHierarchy   ";
            //其他数据库类型 SQLserver、oracle语法，需要去掉 RECURSIVE 关键词
            if (DBTypeConst.ORACLE.equalsIgnoreCase(dbType) || DBTypeConst.SQL_SERVER.equalsIgnoreCase(dbType)) {
                sql = "WITH ManagerHierarchy AS ( " +
                        "    SELECT  " +
                        "        id,  " +
                        "        managerid " +
                        "    FROM  " +
                        "        hrmresource " +
                        "    WHERE  " +
                        "        id = " + userid +
                        "    UNION ALL " +
                        "    SELECT  " +
                        "        h.id,  " +
                        "        h.managerid " +
                        "    FROM  " +
                        "        hrmresource h " +
                        "    INNER JOIN  " +
                        "        ManagerHierarchy mh ON h.id = mh.managerid " +
                        "     where h.id != mh.id " +// --  排除循环引用，但允许最高层的 managerid = id,这里是防止一个人的上级是自己，导致无限循环，这样会查到这种人为止
                        " ) " +
                        " SELECT  " +
                        "    id AS person_id, " +
                        "    managerid AS manager_id " +
                        " FROM  " +
                        "    ManagerHierarchy  ";
            }
            log.error("getAllManagerIdList sql :" + sql);
            if (rs.executeQuery(sql)) {
                while (rs.next()) {
                    result.add(rs.getInt("person_id"));
                }
            } else {
                log.error("getAllManagerIdList sql error:" + rs.getExceptionMsg() + ";sql:" + sql);
            }
        } catch (Exception e) {
            log.error("getAllManagerIdList 异常", e);
        }
        return result;

    }

}
